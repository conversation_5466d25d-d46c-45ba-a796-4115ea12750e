import backupScheduler from '@/lib/backupScheduler';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { userEmail } = req.body;
    
    if (!userEmail) {
      return res.status(400).json({
        success: false,
        error: 'User email is required'
      });
    }

    console.log(`Testing scheduled backup for: ${userEmail}`);

    // Initialize scheduler if not already done
    await backupScheduler.initialize();

    // Test the scheduled backup
    const result = await backupScheduler.testScheduledBackup(userEmail);

    res.status(200).json({
      success: true,
      message: 'Scheduled backup test completed successfully',
      data: {
        backupId: result.id,
        fileName: result.name || result.fileName,
        size: result.size || result.localSize,
        uploadTime: result.uploadTime
      }
    });

  } catch (error) {
    console.error('Test scheduled backup API error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to test scheduled backup',
      message: error.message
    });
  }
}
